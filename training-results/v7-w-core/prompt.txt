

我想写一个python脚本来可视化deep learning训练的metrics.

metric数据在 train_val_log.tsv 文件中（请特别注意这个文件的格式，你可以使用head命令先看一下）。
我希望作出metric vs. epoch的图，包括：

图1: train_total/valid_total vs. epoch
图2: train_bind_n/valid_bind_n/train_bind_cr/valid_bind_n vs. epoch
图3: train_rmsd/valid_rmsd vs. epoch
图4: valid_top1/valid_top5 vs. epoch (value需要乘以100，range: 0-100) 

请参考sample_plot_result.py的作图样式，我希望得到一个1列4行的格式。

请注意：

- 在图中，请对 train 和 valid对应的曲线使用不同的色系以便区分， train的曲线使用'tab:blue', valid曲线使用'lightseagreen'.
- 假如一个图中出现了 train或者valid的不同种曲线，请使用明显不同的point marker 来区分
- 我希望这些子图共享x轴，来节省空间
- 请尽量缩小子图之间的间距
