# `test_akscore2_v4_dude_platform_screen_pred.py` 数据加载和处理流程分析

## 1. 输入文件结构分析

### `.pkl.gz` 文件内部数据结构

从代码中可以看出，脚本从 `glide_graph_one/` 目录读取的 `.pkl.gz` 文件包含一个列表的图对象。关键代码在第175-176行：

```python
with gzip.open(graph_path, 'rb') as f:
    pdb_id_graphs = pickle.load(f)
```

这表明每个 `.pkl.gz` 文件被解压缩后，包含的是一个Python对象，该对象是一个列表（`pdb_id_graphs`），列表中的每个元素是一个图数据对象。

### 数据字段和属性

通过分析 `graph_modification` 函数中处理的数据，可以确定每个图对象包含以下属性：

1. `x`: 节点特征矩阵，表示原子的特征
2. `edge_index`: 边索引矩阵，表示原子间的连接关系
3. `edge_attr`: 边特征矩阵，包含边的类型和其他特征
4. `y`: 虽然在此脚本中没有直接使用，但根据训练脚本的上下文，这应该是RMSD值
5. `name`: 图对象的名称（在第187行被重新赋值为文件名）

在 `graph_modification` 函数中，代码处理了原子特征和边特征，这表明原始数据具有以下具体结构：

- 节点特征 `x` 包含末尾的5个特征（其中1个被移除，保留后4个）
- 边特征 `edge_attr` 的前3个元素用于标识边的类型（蛋白质内部边、配体内部边、蛋白质-配体间边）
- 边特征 `edge_attr` 的后9个元素被移除

### 数据组织方式

从代码可以推断，每个 `.pkl.gz` 文件包含一个列表，列表中的每个元素是一个 PyTorch Geometric 的 `Data` 对象，代表一个蛋白质-配体复合物的图结构。这些图对象包含完整的蛋白质和配体结构的信息，以图的形式表示（节点是原子，边是原子间的相互作用）。

### 文件名的使用

在脚本中，`.pkl.gz` 文件的文件名被以下方式使用：

1. **文件名作为分子标识符**：在 `akscore2_dataset.get()` 方法中（第187行），文件名被提取并赋值给图对象的 `name` 属性：

```python
graph.name = os.path.split(graph_path)[-1].split(".pkl.gz")[0]
```

这行代码从文件路径中提取文件名（不包括扩展名 `.pkl.gz`），并将其设置为图对象的 `name` 属性。这意味着脚本假设文件名本身就是分子或配体的标识符。

2. **在结果输出中使用文件名**：当记录预测结果时，这些文件名（已存储在图对象的 `name` 属性中）被添加到结果字典中：

```python
result["names"].extend(list(chain(*graph_batch.name)))
```

3. **在保存结果时处理文件名**：在 `save_result()` 函数中（第242-243行），文件名还会被进一步处理：

```python
name = result['names'][i]
name = name.split("_")[0] + "_" + name.split("_")[-1]
```

这段代码表明，文件名可能包含多个由下划线分隔的部分，而脚本只保留第一部分和最后一部分，中间的部分被丢弃。这可能是为了提取分子标识符的核心部分，忽略中间的一些描述性信息。

4. **写入结果文件**：处理后的文件名与预测结果一起写入输出文件：

```python
f.write(f"{name}\t{i}\t{result['preds'][i]:.4f}\n")
```

这说明输出结果文件的每一行包含：处理后的分子名称、索引号和对应的预测分数。

文件的命名约定在整个工作流程中是重要的，因为它们直接用于识别和追踪不同的分子结构。

## 2. 数据处理流程追踪

### 从文件加载到处理的流程

1. 在 `akscore2_dataset.get()` 方法中（第164-200行），流程如下：
   
   a. 读取指定的 `.pkl.gz` 文件：
   ```python
   with gzip.open(graph_path, 'rb') as f:
       pdb_id_graphs = pickle.load(f)
   ```
   
   b. 为每个图对象创建四个空列表，用于存储处理后的图数据：
   ```python
   graph_list = []
   protein_graph_list = []
   ligand_graph_list = []
   protein_ligand_graph_list = []
   ```
   
   c. 对每个图对象进行处理：
   ```python
   for graph in pdb_id_graphs:
       graph, protein_graph, ligand_graph, protein_ligand_graph = self.graph_modification(graph)
       # 更新图对象的name属性
       graph.name = os.path.split(graph_path)[-1].split(".pkl.gz")[0]
       # 将处理后的四种图添加到相应的列表中
       graph_list.append(graph)
       protein_graph_list.append(protein_graph)
       ligand_graph_list.append(ligand_graph)
       protein_ligand_graph_list.append(protein_ligand_graph)
   ```
   
   d. 将四个列表中的图分别打包成批次：
   ```python
   graph_list_batch = pyg_batch_func.Batch.from_data_list(graph_list)
   protein_graph_list_batch = pyg_batch_func.Batch.from_data_list(protein_graph_list)
   ligand_graph_list_batch = pyg_batch_func.Batch.from_data_list(ligand_graph_list)
   protein_ligand_graph_list_batch = pyg_batch_func.Batch.from_data_list(protein_ligand_graph_list)
   ```

### `graph_modification()` 函数的变换操作

`graph_modification()` 函数（第129-159行）对原始图数据进行了以下处理：

1. 首先，复制原始图的节点特征、边索引和边特征：
```python
x, edge_index, edge_attr = graph.x.detach().clone(), graph.edge_index.detach().clone(), graph.edge_attr.detach().clone()
```

2. 根据边特征的前三个元素识别边的类型：
```python
protein_edge_attr_idx = torch.where((edge_attr[:, :3] == torch.Tensor([1, 0, 0])).all(dim=1))[0]  # 蛋白质内部边
ligand_edge_attr_idx = torch.where((edge_attr[:, :3] == torch.Tensor([0, 1, 0])).all(dim=1))[0]    # 配体内部边
protein_ligand_edge_attr_idx = torch.where((edge_attr[:, :3] == torch.Tensor([0, 0, 1])).all(dim=1))[0]  # 蛋白质-配体间边
```

3. 移除部分特征（可能是对接相关的特征）：
```python
x = torch.concat((x[:,:-5], x[:,-4:]),axis=1)  # 移除倒数第5个特征，保留最后4个特征
edge_attr = edge_attr[:,3:-9]  # 移除前3个和后9个边特征
```

4. 提取不同类型的边索引：
```python
protein_edge_index = edge_index[:, protein_edge_attr_idx]
ligand_edge_index = edge_index[:, ligand_edge_attr_idx]
protein_ligand_edge_index = edge_index[:, protein_ligand_edge_attr_idx]
```

5. 根据配体边的最小节点索引，区分蛋白质和配体节点：
```python
protein_ligand_node_sep_idx = torch.min(ligand_edge_index)
protein_x = x[:protein_ligand_node_sep_idx, :]
ligand_x = x[protein_ligand_node_sep_idx:, :]
```

6. 创建四种不同的图数据对象：
   - 原始图（可能已经修改了特征）
   - 蛋白质图（只包含蛋白质节点和边）
   - 配体图（只包含配体节点和边，且重新索引）
   - 蛋白质-配体相互作用图（包含所有节点，但只有蛋白质-配体间的边）

```python
protein_graph = Data(x=protein_x, edge_index=protein_edge_index, edge_attr=edge_attr[protein_edge_attr_idx,:])
ligand_graph = Data(x=ligand_x, edge_index=ligand_edge_index-torch.min(ligand_edge_index), edge_attr=edge_attr[ligand_edge_attr_idx,:])
protein_ligand_graph = Data(x=x, edge_index=protein_ligand_edge_index, edge_attr=protein_ligand_edge_attr)
```

## 3. 关键代码段分析

### 特征处理分析

1. **节点特征 (x) 处理**：
```python
x = torch.concat((x[:,:-5], x[:,-4:]),axis=1)
```
这行代码移除了原始节点特征的倒数第5个特征，同时保留了最后4个特征。根据注释，这似乎是为了移除对接相关的特征（"remove docking feature for platform app"）。

2. **边特征 (edge_attr) 处理**：
```python
edge_attr = edge_attr[:,3:-9]
```
这行代码移除了边特征的前3个元素（边类型标识）和后9个元素。前3个元素已经用于识别边的类型，因此不再需要；后9个元素可能是某些对接相关的特征，在此应用中不需要。

### 图拆分过程

```python
protein_ligand_node_sep_idx = torch.min(ligand_edge_index)
protein_x = x[:protein_ligand_node_sep_idx, :]
ligand_x = x[protein_ligand_node_sep_idx:, :]
```

这段代码揭示了原始图中的节点排序方式：蛋白质节点在前，配体节点在后。通过找到配体边中最小的节点索引，可以确定蛋白质和配体节点的分界点。

### 配体图的重新索引

```python
ligand_graph = Data(x=ligand_x, edge_index=ligand_edge_index-torch.min(ligand_edge_index), edge_attr=edge_attr[ligand_edge_attr_idx,:])
```

对于配体图，边索引被减去最小索引值，这样配体图的节点索引就从0开始，保持了图的连接性但使节点索引连续。

### 数据流到模型预测

在主要的测试循环中（第317-327行），数据流程如下：

1. 从DataLoader获取批次数据，包括四种处理后的图：
```python
for idx, (graph_batch, protein_graph_batch, ligand_graph_batch, protein_ligand_graph_batch) in enumerate(loader):
```

2. 将图数据输入模型进行预测：
```python
rmsd_logits = model(graph_batch.to(DEVICE), protein_graph_batch.to(DEVICE),
                    ligand_graph_batch.to(DEVICE), protein_ligand_graph_batch.to(DEVICE))
```

3. 应用sigmoid激活函数，将输出转换为概率值（表示是"好"构象的概率）：
```python
rmsd_logits = torch.sigmoid(rmsd_logits)
```

4. 记录预测结果和对应的名称：
```python
result["preds"].extend([x.item() for x in rmsd_logits])
result["names"].extend(list(chain(*graph_batch.name)))
```

5. 最后，将结果保存到文件中：
```python
save_result(result_save_dir, result, target_name)
```

## 总结

`test_akscore2_v4_dude_platform_screen_pred.py` 脚本的数据加载和处理流程如下：

1. 从 `glide_graph_one/` 目录读取压缩的 `.pkl.gz` 文件，每个文件包含一个列表的图对象。
2. 每个图对象代表一个蛋白质-配体复合物，包含节点特征（原子属性）、边索引（原子连接）和边特征（连接属性）。
3. 通过 `graph_modification()` 函数，将原始复合物图拆分为四种图：原始图、蛋白质图、配体图和蛋白质-配体相互作用图。
4. 在拆分过程中，移除了某些可能与对接相关的特征，并根据边的类型对图进行了分类处理。
5. 处理后的四种图被分别打包成批次，输入到模型中进行预测。
6. 模型输出经过sigmoid函数处理后，作为最终的预测结果（表示构象质量的得分）。
7. 预测结果与对应的分子名称一起保存到文件中。

这个流程展示了如何将复杂的蛋白质-配体相互作用数据转换为适合图神经网络处理的结构，并通过模型预测构象的质量，用于虚拟筛选任务。